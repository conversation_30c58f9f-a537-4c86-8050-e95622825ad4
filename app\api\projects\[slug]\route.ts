import { NextResponse } from 'next/server'
import projectsData from '@/data/projects.json'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    // Simulate API delay (optional - remove in production)
    await new Promise(resolve => setTimeout(resolve, 100))

    const { slug } = await params
    const project = projectsData.projects.find(p => p.slug === slug)

    if (!project) {
      return NextResponse.json(
        {
          success: false,
          error: 'Project not found'
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: project
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch project'
      },
      { status: 500 }
    )
  }
}
