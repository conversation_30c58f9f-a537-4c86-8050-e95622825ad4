import { Project, RelatedProject } from '@/lib/projects'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || ''

// API response types
interface ApiResponse<T> {
  success: boolean
  data: T
  total?: number
  error?: string
}

// Helper function to build API URL
function buildApiUrl(endpoint: string): string {
  if (API_BASE_URL) {
    return `${API_BASE_URL}${endpoint}`
  }
  // For server-side rendering, use localhost
  if (typeof window === 'undefined') {
    return `http://localhost:3000/api${endpoint}`
  }
  // For client-side, use relative URLs
  return `/api${endpoint}`
}

// Helper function to handle API responses
async function handleApiResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}))
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`)
  }

  const result: ApiResponse<T> = await response.json()

  if (!result.success) {
    throw new Error(result.error || 'API request failed')
  }

  return result.data
}

// Fetch all projects
export async function fetchAllProjects(): Promise<Project[]> {
  try {
    const response = await fetch(buildApiUrl('/projects'), {
      next: { revalidate: 300 } // Cache for 5 minutes
    })

    return await handleApiResponse<Project[]>(response)
  } catch (error) {
    console.error('Failed to fetch projects:', error)
    throw error
  }
}

// Fetch a single project by slug
export async function fetchProjectBySlug(slug: string): Promise<Project | null> {
  try {
    const response = await fetch(buildApiUrl(`/projects/${slug}`), {
      next: { revalidate: 300 } // Cache for 5 minutes
    })

    return await handleApiResponse<Project>(response)
  } catch (error) {
    if (error instanceof Error && error.message.includes('404')) {
      return null
    }
    console.error(`Failed to fetch project ${slug}:`, error)
    throw error
  }
}

// Fetch related projects for a given project slug
export async function fetchRelatedProjects(slug: string, limit = 2): Promise<RelatedProject[]> {
  try {
    const response = await fetch(buildApiUrl(`/projects/${slug}/related?limit=${limit}`), {
      next: { revalidate: 300 } // Cache for 5 minutes
    })

    return await handleApiResponse<RelatedProject[]>(response)
  } catch (error) {
    console.error(`Failed to fetch related projects for ${slug}:`, error)
    throw error
  }
}

// Client-side versions (for use in components with useEffect, etc.)
export const clientApi = {
  async getAllProjects(): Promise<Project[]> {
    const response = await fetch('/api/projects')
    return await handleApiResponse<Project[]>(response)
  },

  async getProjectBySlug(slug: string): Promise<Project | null> {
    try {
      const response = await fetch(`/api/projects/${slug}`)
      return await handleApiResponse<Project>(response)
    } catch (error) {
      if (error instanceof Error && error.message.includes('404')) {
        return null
      }
      throw error
    }
  },

  async getRelatedProjects(slug: string, limit = 2): Promise<RelatedProject[]> {
    const response = await fetch(`/api/projects/${slug}/related?limit=${limit}`)
    return await handleApiResponse<RelatedProject[]>(response)
  }
}
