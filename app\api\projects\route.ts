import { NextResponse } from 'next/server'
import projectsData from '@/data/projects.json'

export async function GET() {
  try {
    // Simulate API delay (optional - remove in production)
    await new Promise(resolve => setTimeout(resolve, 100))
    
    return NextResponse.json({
      success: true,
      data: projectsData.projects,
      total: projectsData.projects.length
    })
  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch projects' 
      },
      { status: 500 }
    )
  }
}
