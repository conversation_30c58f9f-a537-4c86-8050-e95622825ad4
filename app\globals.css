@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 0%;
  --foreground: 0 0% 100%;
  --card: 240 10% 4%;
  --card-foreground: 0 0% 100%;
  --popover: 240 10% 4%;
  --popover-foreground: 0 0% 100%;
  --primary: 186 100% 50%;
  --primary-foreground: 0 0% 0%;
  --secondary: 240 5% 10%;
  --secondary-foreground: 0 0% 100%;
  --muted: 240 5% 20%;
  --muted-foreground: 240 5% 65%;
  --accent: 240 5% 15%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 100% 50%;
  --destructive-foreground: 0 0% 100%;
  --border: 240 5% 26%;
  --input: 240 5% 26%;
  --ring: 186 100% 50%;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: black;
  color: white;
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Custom Scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-zinc-700::-webkit-scrollbar-thumb {
    background-color: #3f3f46;
    border-radius: 3px;
  }

  .scrollbar-track-zinc-800\/50::-webkit-scrollbar-track {
    background-color: rgba(39, 39, 42, 0.5);
  }

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Add animation for glowing effects */
@keyframes pulse-glow {
  0%,
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}
