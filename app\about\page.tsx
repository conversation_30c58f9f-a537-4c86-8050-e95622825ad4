import Image from "next/image"
import Link from "next/link"
import { ArrowLeft, Award, GraduationCap, Briefcase, User, Languages, Heart } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { SkillTag } from "@/components/skill-tag"
import { EnhancedScrollIndicator } from "@/components/enhanced-scroll-indicator"
import { AnimatedSection } from "@/components/animated-section"
import { PortfolioHeader } from "@/components/portfolio-header"
import { SocialLinks } from "@/components/social-links"
import { ExperienceCard } from "@/components/experience-card"
import {
  getPersonalInfo,
  getAboutInfo,
  getExperienceInfo,
  getCredentialsInfo,
  getTechnicalSkillsInfo,
} from "@/lib/data"

export default function AboutPage() {
  const personalInfo = getPersonalInfo()
  const aboutInfo = getAboutInfo()
  const experienceInfo = getExperienceInfo()
  const credentialsInfo = getCredentialsInfo()
  const technicalSkills = getTechnicalSkillsInfo()

  return (
    <main className="min-h-screen bg-black text-white">
      {/* Background Grid Pattern */}
      <div className="fixed inset-0 bg-[radial-gradient(#333_1px,transparent_1px)] [background-size:20px_20px] opacity-20 z-0"></div>

      {/* Header */}
      <PortfolioHeader />

      <div className="relative z-10 container mx-auto p-3 sm:p-4 pt-20 sm:pt-24 pb-6 sm:pb-8">
        {/* Back Button */}
        <AnimatedSection animation="fade-in">
          <Link
            href="/"
            className="inline-flex items-center text-xs sm:text-sm text-zinc-400 hover:text-white mb-4 sm:mb-6 transition-colors"
          >
            <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            Back to Home
          </Link>
        </AnimatedSection>

        {/* Page Title */}
        <AnimatedSection animation="fade-up" className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-green-400 to-emerald-500">
            About Me
          </h1>
          <p className="text-zinc-400 mt-2 max-w-2xl">
            Learn more about my background, experience and skills
          </p>
        </AnimatedSection>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {/* Profile Section */}
          <AnimatedSection animation="fade-up" delay={100}>
            <Card className="bg-zinc-900/70 border-zinc-800 backdrop-blur-sm overflow-hidden">
              <CardContent className="p-0">
                {/* Profile Header */}
                <div className="bg-gradient-to-r from-zinc-800/50 to-zinc-900/50 p-6 flex flex-col items-center border-b border-zinc-800">
                  <div className="relative w-32 h-32 rounded-full overflow-hidden mb-4 border-2 border-green-400/20 ring-4 ring-zinc-800/50">
                    <Image
                      src={personalInfo.avatar || "/placeholder.svg"}
                      alt={personalInfo.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="text-center">
                    <h2 className="text-2xl font-bold">{personalInfo.name}</h2>
                    <p className="text-green-400 mb-2">{personalInfo.title}</p>
                    <div className="flex flex-wrap gap-2 justify-center mb-4">
                      {personalInfo.badges.map((badge, index) => (
                        <Badge key={index} variant="outline" className="bg-zinc-800/50 hover:bg-zinc-700">
                          {badge}
                        </Badge>
                      ))}
                    </div>
                    <SocialLinks socialLinks={personalInfo.social} />
                  </div>
                </div>

                {/* Bio Section */}
                <div className="p-6 space-y-6">
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-zinc-400 flex items-center">
                      <User className="w-4 h-4 mr-2 text-green-400" />
                      Biography
                    </h3>
                    <p className="text-sm">{aboutInfo.bio}</p>
                  </div>

                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-zinc-400 flex items-center">
                      <Briefcase className="w-4 h-4 mr-2 text-green-400" />
                      Professional Focus
                    </h3>
                    <div className="space-y-2">
                      {aboutInfo.focus.map((item, index) => (
                        <div key={index} className="flex items-start">
                          <span className="text-green-400 mr-2">•</span>
                          <p className="text-sm">{item}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-zinc-400 flex items-center">
                      <Languages className="w-4 h-4 mr-2 text-green-400" />
                      Languages
                    </h3>
                    <div className="space-y-3">
                      {aboutInfo.languages.map((language, index) => (
                        <div key={index} className="space-y-1">
                          <div className="flex justify-between items-center">
                            <span className="text-sm">
                              {language.flag} {language.name}
                            </span>
                            <span className="text-xs text-zinc-400">{language.proficiency}</span>
                          </div>
                          <div className="h-1.5 bg-zinc-800 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"
                              style={{ width: `${language.level}%` }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-zinc-400 flex items-center">
                      <Heart className="w-4 h-4 mr-2 text-green-400" />
                      Interests
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {aboutInfo.interests.map((interest, index) => (
                        <SkillTag key={index}>{interest}</SkillTag>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Availability Status */}
                <div className="p-4 border-t border-zinc-800 flex items-center justify-center">
                  <div className="flex items-center">
                    <span
                      className={`w-2 h-2 ${
                        personalInfo.availableForWork ? "bg-green-500" : "bg-red-500"
                      } rounded-full mr-2`}
                    ></span>
                    <span className="text-xs text-zinc-400">
                      {personalInfo.availableForWork ? "Available for new projects" : "Not available for new projects"}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AnimatedSection>

          <div className="lg:col-span-2 space-y-4 sm:space-y-6">
            {/* Experience Section */}
            <AnimatedSection animation="fade-up" delay={200}>
              <Card className="bg-zinc-900/70 border-zinc-800 backdrop-blur-sm">
                <CardContent className="p-4 sm:p-6">
                  <div className="flex items-center mb-4 sm:mb-6">
                    <Briefcase className="w-5 h-5 mr-2 text-green-400" />
                    <h3 className="text-lg font-medium">Professional Experience</h3>
                  </div>

                  <div className="space-y-6 sm:space-y-8">
                    {experienceInfo.map((experience, index) => (
                      <AnimatedSection key={index} animation="fade-up" delay={100 * (index + 1)}>
                        <ExperienceCard
                          title={experience.title}
                          company={experience.company}
                          period={experience.period}
                          description={experience.description}
                          achievements={experience.achievements}
                          technologies={experience.technologies}
                        />
                      </AnimatedSection>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </AnimatedSection>

            {/* Education & Certifications */}
            <AnimatedSection animation="fade-up" delay={300}>
              <Card className="bg-zinc-900/70 border-zinc-800 backdrop-blur-sm">
                <CardContent className="p-4 sm:p-6">
                  <div className="flex items-center mb-4 sm:mb-6">
                    <GraduationCap className="w-5 h-5 mr-2 text-green-400" />
                    <h3 className="text-lg font-medium">Education & Certifications</h3>
                  </div>

                  <div className="space-y-6">
                    {/* Education */}
                    <div className="space-y-4">
                      <h4 className="text-sm font-medium text-zinc-400 flex items-center border-b border-zinc-800 pb-2">
                        <GraduationCap className="w-4 h-4 mr-2 text-green-400" />
                        Education
                      </h4>
                      <div className="space-y-3 sm:space-y-4">
                        {credentialsInfo.education.map((edu, index) => (
                          <AnimatedSection key={index} animation="fade-up" delay={100 * (index + 1)}>
                            <div className="flex items-start bg-zinc-800/30 p-3 rounded-lg">
                              {edu.logo && (
                                <div className="relative w-12 h-12 rounded overflow-hidden mr-3 flex-shrink-0 bg-zinc-800">
                                  <Image
                                    src={edu.logo || "/placeholder.svg"}
                                    alt={edu.institution}
                                    fill
                                    className="object-contain p-1"
                                  />
                                </div>
                              )}
                              <div>
                                <h5 className="font-medium">{edu.degree}</h5>
                                <p className="text-sm text-zinc-400">
                                  {edu.institution} • {edu.year}
                                </p>
                              </div>
                            </div>
                          </AnimatedSection>
                        ))}
                      </div>
                    </div>

                    {/* Certifications */}
                    <div className="space-y-4">
                      <h4 className="text-sm font-medium text-zinc-400 flex items-center border-b border-zinc-800 pb-2">
                        <Award className="w-4 h-4 mr-2 text-green-400" />
                        Professional Certifications
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                        {credentialsInfo.certifications.map((cert, index) => (
                          <AnimatedSection key={index} animation="fade-up" delay={100 * (index + 1)}>
                            <div className="flex items-start bg-zinc-800/30 p-3 rounded-lg">
                              {cert.logo && (
                                <div className="relative w-12 h-12 rounded overflow-hidden mr-3 flex-shrink-0 bg-zinc-800">
                                  <Image
                                    src={cert.logo || "/placeholder.svg"}
                                    alt={cert.issuer}
                                    fill
                                    className="object-contain p-1"
                                  />
                                </div>
                              )}
                              <div>
                                <h5 className="font-medium">{cert.name}</h5>
                                <p className="text-sm text-zinc-400">
                                  {cert.issuer} • {cert.date}
                                </p>
                              </div>
                            </div>
                          </AnimatedSection>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </AnimatedSection>

            {/* Technical Skills */}
            <AnimatedSection animation="fade-up" delay={400}>
              <Card className="bg-zinc-900/70 border-zinc-800 backdrop-blur-sm">
                <CardContent className="p-4 sm:p-6">
                  <div className="flex items-center mb-4 sm:mb-6">
                    <Award className="w-5 h-5 mr-2 text-green-400" />
                    <h3 className="text-lg font-medium">Technical Skills</h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-1 gap-4 sm:gap-6">
                    {/* <AnimatedSection animation="slide-right" delay={100}>
                      <div className="space-y-3">
                        <h4 className="text-sm font-medium text-zinc-400">Design</h4>
                        <div className="flex flex-wrap gap-2">
                          {technicalSkills.design.map((skill, index) => (
                            <SkillTag key={index}>{skill}</SkillTag>
                          ))}
                        </div>
                      </div>
                    </AnimatedSection> */}

                    <AnimatedSection animation="slide-left" delay={200}>
                      <div className="space-y-3">
                        <h4 className="text-sm font-medium text-zinc-400">Development</h4>
                        <div className="flex flex-wrap gap-2">
                          {technicalSkills.development.map((skill, index) => (
                            <SkillTag key={index}>{skill}</SkillTag>
                          ))}
                        </div>
                      </div>
                    </AnimatedSection>

                    {/* <AnimatedSection animation="slide-right" delay={300}>
                      <div className="space-y-3">
                        <h4 className="text-sm font-medium text-zinc-400">UX Methods</h4>
                        <div className="flex flex-wrap gap-2">
                          {technicalSkills.uxMethods.map((skill, index) => (
                            <SkillTag key={index}>{skill}</SkillTag>
                          ))}
                        </div>
                      </div>
                    </AnimatedSection> */}

                    {/* <AnimatedSection animation="slide-left" delay={400}>
                      <div className="space-y-3">
                        <h4 className="text-sm font-medium text-zinc-400">Soft Skills</h4>
                        <div className="flex flex-wrap gap-2">
                          {technicalSkills.softSkills.map((skill, index) => (
                            <SkillTag key={index}>{skill}</SkillTag>
                          ))}
                        </div>
                      </div>
                    </AnimatedSection> */}
                  </div>
                </CardContent>
              </Card>
            </AnimatedSection>

            {/* CTA Section */}
            <AnimatedSection animation="fade-up" delay={500}>
              <Card className="bg-gradient-to-r from-green-900/30 to-emerald-900/30 border-green-800/30 backdrop-blur-sm">
                <CardContent className="p-6 sm:p-8 text-center">
                  <h3 className="text-xl font-bold mb-2">Interested in working together?</h3>
                  <p className="text-zinc-300 mb-4 max-w-lg mx-auto">
                    I'm always open to discussing new projects, creative ideas or opportunities to be part of your
                    vision.
                  </p>
                  <Button
                    asChild
                    className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
                  >
                    <Link href="/contact">Get in Touch</Link>
                  </Button>
                </CardContent>
              </Card>
            </AnimatedSection>
          </div>
        </div>

        {/* Footer */}
        <AnimatedSection
          animation="fade-in"
          delay={600}
          className="mt-8 sm:mt-12 py-4 sm:py-6 text-center text-xs sm:text-sm text-zinc-500"
        >
          <p>© {new Date().getFullYear()} Ariful Islam. All rights reserved.</p>
        </AnimatedSection>
      </div>

      {/* Scroll to Top Button */}
      <EnhancedScrollIndicator />
    </main>
  )
}
