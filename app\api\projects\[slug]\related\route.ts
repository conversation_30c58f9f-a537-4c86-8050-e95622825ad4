import { NextResponse } from 'next/server'
import projectsData from '@/data/projects.json'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    // Simulate API delay (optional - remove in production)
    await new Promise(resolve => setTimeout(resolve, 100))

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '2')

    const { slug } = await params
    const currentProject = projectsData.projects.find(p => p.slug === slug)

    if (!currentProject) {
      return NextResponse.json(
        {
          success: false,
          error: 'Project not found'
        },
        { status: 404 }
      )
    }

    let relatedProjects

    if (currentProject.relatedProjects && currentProject.relatedProjects.length > 0) {
      // Use predefined related projects
      relatedProjects = currentProject.relatedProjects.slice(0, limit)
    } else {
      // Generate related projects from other projects
      relatedProjects = projectsData.projects
        .filter(project => project.slug !== slug)
        .slice(0, limit)
        .map(project => ({
          slug: project.slug,
          title: project.title,
          category: project.category,
          image: project.thumbnailImage
        }))
    }

    return NextResponse.json({
      success: true,
      data: relatedProjects,
      total: relatedProjects.length
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch related projects'
      },
      { status: 500 }
    )
  }
}
