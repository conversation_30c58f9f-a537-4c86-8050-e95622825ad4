"use client"

import type React from "react"
import { useState, useEffect } from "react"
import Link from "next/link"
import { GlobeIcon, CodeIcon, BriefcaseIcon, ArrowRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ProjectCard } from "@/components/project-card"
import { clientApi } from "@/lib/api/projects"
import type { Project } from "@/lib/projects"
import { ExperienceCard } from "@/components/experience-card"
import { EnhancedScrollIndicator } from "@/components/enhanced-scroll-indicator"
import { AnimatedSection } from "@/components/animated-section"
import { EnhancedProfile } from "@/components/enhanced-profile"
import { CredentialsSection } from "@/components/credentials-section"
import { PortfolioHeader } from "@/components/portfolio-header"
import { getExperienceInfo, getTechnicalSkillsInfo } from "@/lib/data"

const SkillTagComponent = ({ children }: { children: React.ReactNode }) => {
  return <div className="px-2 py-1 bg-zinc-800 rounded-full text-xs font-medium text-zinc-400">{children}</div>
}

export default function Home() {
  const [projects, setProjects] = useState<Project[]>([])
  const [projectsLoading, setProjectsLoading] = useState(true)
  const experienceInfo = getExperienceInfo()
  const technicalSkills = getTechnicalSkillsInfo()

  // Fetch projects from API
  useEffect(() => {
    async function fetchProjects() {
      try {
        setProjectsLoading(true)
        const allProjects = await clientApi.getAllProjects()
        setProjects(allProjects.slice(0, 3)) // Only show first 3 projects on homepage
      } catch (error) {
        console.error('Failed to fetch projects:', error)
        // Fallback to empty array on error
        setProjects([])
      } finally {
        setProjectsLoading(false)
      }
    }

    fetchProjects()
  }, [])

  return (
    <main className="min-h-screen bg-black text-white">
      {/* Background Grid Pattern */}
      <div className="fixed inset-0 bg-[radial-gradient(#333_1px,transparent_1px)] [background-size:20px_20px] opacity-20 z-0"></div>

      {/* Header */}
      <PortfolioHeader />

      <div className="relative z-10 container mx-auto p-3 sm:p-4 pt-20 sm:pt-24 pb-6 sm:pb-8">
        {/* Main Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
          {/* Enhanced Profile Section */}
          <div className="md:sticky md:top-24 self-start">
            <AnimatedSection animation="slide-right">
              <EnhancedProfile />
            </AnimatedSection>
          </div>

          <div className="col-span-1 md:col-span-2 lg:col-span-3 space-y-4 sm:space-y-6">
            {/* Experience Section - Expanded */}
            <AnimatedSection animation="fade-up" id="experience">
              <Card className="bg-zinc-900/70 border-zinc-800 backdrop-blur-sm">
                <CardContent className="p-4 sm:p-6">
                  <div className="flex items-center justify-between mb-4 sm:mb-6">
                    <div className="flex items-center">
                      <BriefcaseIcon className="w-5 h-5 mr-2 text-green-400" />
                      <h3 className="text-lg font-medium">Experience</h3>
                    </div>
                    <Button variant="ghost" size="sm" asChild className="text-xs sm:text-sm px-2 sm:px-3">
                      <Link href="/about#experience">
                        View All
                        <ArrowRight className="w-3 h-3 ml-1" />
                      </Link>
                    </Button>
                  </div>

                  <div className="space-y-6 sm:space-y-8">
                    {experienceInfo.slice(0, 2).map((experience, index) => (
                      <AnimatedSection key={index} animation="fade-up" delay={100 * (index + 1)}>
                        <ExperienceCard
                          title={experience.title}
                          company={experience.company}
                          period={experience.period}
                          description={experience.description}
                          achievements={experience.achievements}
                          technologies={experience.technologies}
                        />
                      </AnimatedSection>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </AnimatedSection>

            {/* Credentials Section */}
            <AnimatedSection animation="fade-up" id="credentials">
              <CredentialsSection />
            </AnimatedSection>

            {/* Skills Section */}
            <AnimatedSection animation="fade-up" id="skills">
              <Card className="bg-zinc-900/70 border-zinc-800 backdrop-blur-sm">
                <CardContent className="p-4 sm:p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <CodeIcon className="w-5 h-5 mr-2 text-green-400" />
                      <h3 className="text-lg font-medium">Technical Skills</h3>
                    </div>
                    {/* <Button variant="ghost" size="sm" asChild className="text-xs sm:text-sm px-2 sm:px-3">
                      <Link href="/about#skills">
                        View All
                        <ArrowRight className="w-3 h-3 ml-1" />
                      </Link>
                    </Button> */}
                  </div>

                  <div className="grid grid-cols-1 gap-4 sm:gap-6">
                    {/* <AnimatedSection animation="slide-right" delay={100}>
                      <div className="space-y-3">
                        <h4 className="text-sm font-medium text-zinc-400">Design</h4>
                        <div className="flex flex-wrap gap-2">
                          {technicalSkills.design.map((skill, index) => (
                            <SkillTagComponent key={index}>{skill}</SkillTagComponent>
                          ))}
                        </div>
                      </div>
                    </AnimatedSection> */}

                    <AnimatedSection animation="slide-left" delay={200}>
                      <div className="space-y-3">
                        <h4 className="text-sm font-medium text-zinc-400">Development</h4>
                        <div className="flex flex-wrap gap-2">
                          {technicalSkills.development.map((skill, index) => (
                            <SkillTagComponent key={index}>{skill}</SkillTagComponent>
                          ))}
                        </div>
                      </div>
                    </AnimatedSection>
                  </div>
                </CardContent>
              </Card>
            </AnimatedSection>

            {/* Projects Section */}
            <AnimatedSection animation="fade-up" id="projects">
              <Card className="bg-zinc-900/70 border-zinc-800 backdrop-blur-sm">
                <CardContent className="p-4 sm:p-6">
                  <div className="flex items-center justify-between mb-4 sm:mb-6">
                    <div className="flex items-center">
                      <GlobeIcon className="w-5 h-5 mr-2 text-green-400" />
                      <h3 className="text-lg font-medium">Recent Projects</h3>
                    </div>
                    <Button variant="ghost" size="sm" asChild className="text-xs sm:text-sm px-2 sm:px-3">
                      <Link href="/projects">
                        View All
                        <ArrowRight className="w-3 h-3 ml-1" />
                      </Link>
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                    {projectsLoading ? (
                      // Loading skeleton
                      [...Array(3)].map((_, index) => (
                        <div key={index} className="bg-zinc-800/50 border border-zinc-700 rounded-lg h-48 animate-pulse">
                          <div className="h-32 bg-zinc-700 rounded-t-lg"></div>
                          <div className="p-3 space-y-2">
                            <div className="h-4 bg-zinc-700 rounded w-3/4"></div>
                            <div className="h-3 bg-zinc-700 rounded w-1/2"></div>
                          </div>
                        </div>
                      ))
                    ) : projects.length > 0 ? (
                      projects.map((project: Project, index: number) => (
                        <AnimatedSection key={project.id} animation="zoom-in" delay={100 * (index + 1)}>
                          <ProjectCard
                            title={project.title}
                            category={project.category}
                            image={project.thumbnailImage}
                            slug={project.slug}
                          />
                        </AnimatedSection>
                      ))
                    ) : (
                      // Error/empty state
                      <div className="col-span-1 sm:col-span-2 lg:col-span-3 text-center py-8">
                        <p className="text-zinc-400">No projects available at the moment.</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </AnimatedSection>
          </div>
        </div>

        {/* Footer */}
        <AnimatedSection
          animation="fade-in"
          delay={500}
          className="mt-8 sm:mt-12 py-4 sm:py-6 text-center text-xs sm:text-sm text-zinc-500"
        >
          <p>© {new Date().getFullYear()} Ariful Islam. All rights reserved.</p>
        </AnimatedSection>
      </div>

      {/* Scroll to Top Button */}
      <EnhancedScrollIndicator />
    </main>
  )
}
