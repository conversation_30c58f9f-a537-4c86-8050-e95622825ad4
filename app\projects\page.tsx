"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON><PERSON><PERSON>, Filter, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ProjectCard } from "@/components/project-card"
import { EnhancedScrollIndicator } from "@/components/enhanced-scroll-indicator"
import { AnimatedSection } from "@/components/animated-section"
import { PortfolioHeader } from "@/components/portfolio-header"
import { getAllProjects } from "@/lib/data"
import { Badge } from "@/components/ui/badge"

export default function ProjectsPage() {
  const allProjects = getAllProjects()

  // Extract unique categories from projects
  const categories = Array.from(new Set(allProjects.map((project) => project.category)))

  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")

  // Filter projects based on selected category and search query
  const filteredProjects = allProjects.filter((project) => {
    const matchesCategory = selectedCategory ? project.category === selectedCategory : true
    const matchesSearch = searchQuery
      ? project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.shortDescription.toLowerCase().includes(searchQuery.toLowerCase())
      : true
    return matchesCategory && matchesSearch
  })

  return (
    <main className="min-h-screen bg-black text-white">
      {/* Background Grid Pattern */}
      <div className="fixed inset-0 bg-[radial-gradient(#333_1px,transparent_1px)] [background-size:20px_20px] opacity-20 z-0"></div>

      {/* Header */}
      <PortfolioHeader />

      <div className="relative z-10 container mx-auto p-3 sm:p-4 pt-20 sm:pt-24 pb-6 sm:pb-8">
        {/* Back Button */}
        <AnimatedSection animation="fade-in">
          <Link
            href="/"
            className="inline-flex items-center text-xs sm:text-sm text-zinc-400 hover:text-white mb-4 sm:mb-6 transition-colors"
          >
            <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            Back to Home
          </Link>
        </AnimatedSection>

        {/* Page Title */}
        <AnimatedSection animation="fade-up" className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-green-400 to-emerald-500">
            My Projects
          </h1>
          <p className="text-zinc-400 mt-2 max-w-2xl">Explore my portfolio of design and development projects.</p>
        </AnimatedSection>

        {/* Filters */}
        <AnimatedSection animation="fade-up" delay={100} className="mb-6">
          <Card className="bg-zinc-900/70 border-zinc-800 backdrop-blur-sm">
            <CardContent className="p-4 sm:p-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex items-center">
                  <Filter className="w-4 h-4 mr-2 text-green-400" />
                  <h3 className="text-sm font-medium">Filter Projects</h3>
                </div>

                {selectedCategory && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedCategory(null)}
                    className="text-xs self-start sm:self-auto"
                  >
                    <X className="w-3 h-3 mr-1" />
                    Clear Filter
                  </Button>
                )}
              </div>

              <div className="mt-4">
                <input
                  type="text"
                  placeholder="Search projects..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full p-2 bg-zinc-800 border border-zinc-700 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500"
                />
              </div>

              <div className="mt-4 flex flex-wrap gap-2">
                {categories.map((category, index) => (
                  <Badge
                    key={index}
                    variant={selectedCategory === category ? "default" : "outline"}
                    className={`cursor-pointer ${
                      selectedCategory === category
                        ? "bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-white border-green-500/50"
                        : "bg-zinc-800/50 hover:bg-zinc-700 text-zinc-300"
                    }`}
                    onClick={() => setSelectedCategory(category === selectedCategory ? null : category)}
                  >
                    {category}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </AnimatedSection>

        {/* Projects Grid */}
        <AnimatedSection animation="fade-up" delay={200}>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {filteredProjects.length > 0 ? (
              filteredProjects.map((project, index) => (
                <AnimatedSection key={project.id} animation="zoom-in" delay={100 * (index + 1)}>
                  <ProjectCard
                    title={project.title}
                    category={project.category}
                    image={project.thumbnailImage}
                    slug={project.slug}
                  />
                </AnimatedSection>
              ))
            ) : (
              <div className="col-span-1 sm:col-span-2 lg:col-span-3 text-center py-12">
                <div className="w-16 h-16 mx-auto rounded-full bg-zinc-800 flex items-center justify-center mb-4">
                  <X className="w-6 h-6 text-zinc-400" />
                </div>
                <h3 className="text-lg font-medium mb-2">No projects found</h3>
                <p className="text-zinc-400 max-w-md mx-auto">
                  No projects match your current filters. Try adjusting your search criteria or clear the filters.
                </p>
                {(selectedCategory || searchQuery) && (
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => {
                      setSelectedCategory(null)
                      setSearchQuery("")
                    }}
                  >
                    Clear All Filters
                  </Button>
                )}
              </div>
            )}
          </div>
        </AnimatedSection>

        {/* Footer */}
        <AnimatedSection
          animation="fade-in"
          delay={500}
          className="mt-8 sm:mt-12 py-4 sm:py-6 text-center text-xs sm:text-sm text-zinc-500"
        >
          <p>© {new Date().getFullYear()} Ariful Islam. All rights reserved.</p>
        </AnimatedSection>
      </div>

      {/* Scroll to Top Button */}
      <EnhancedScrollIndicator />
    </main>
  )
}
